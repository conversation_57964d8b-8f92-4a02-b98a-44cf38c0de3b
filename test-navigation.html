<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Navigation Test</title>
    <style>
        .nav-primary { background: #333; padding: 10px; }
        .nav-secondary { background: #666; padding: 10px; }
        .nav-primary .menu-item { display: inline-block; margin-right: 20px; }
        .nav-primary .menu-item a { color: white; text-decoration: none; }
        .nav-secondary .sub-menu { display: flex; gap: 10px; }
        .nav-secondary .sub-menu.hidden { display: none; }
        .nav-secondary .sub-menu a { color: white; text-decoration: none; }
        .nav-secondary > .menu-item > a { display: none; }
    </style>
</head>
<body>
    <div id="header">
        <!-- Primary Navigation -->
        <nav class="nav-primary">
            <ul class="nav">
                <li class="menu-item menu-item-type-custom menu-item-object-custom menu-item-has-children menu-item-13">
                    <a href="#">About Festival</a>
                    <ul class="sub-menu">
                        <li class="menu-item menu-item-type-post_type menu-item-object-page menu-item-179">
                            <a href="#">Ukážková stránka</a>
                        </li>
                        <li class="menu-item menu-item-type-post_type menu-item-object-page menu-item-180">
                            <a href="#">Ukážková stránka</a>
                        </li>
                        <li class="menu-item menu-item-type-post_type menu-item-object-post menu-item-181">
                            <a href="#">Test Post 2 Recomendation</a>
                        </li>
                    </ul>
                </li>
                <li class="menu-item menu-item-type-custom menu-item-object-custom menu-item-has-children menu-item-14">
                    <a href="#">Art & Tech conference</a>
                    <ul class="sub-menu">
                        <li class="menu-item menu-item-type-post_type menu-item-object-post menu-item-182">
                            <a href="#">Test Post 1 Recomendation</a>
                        </li>
                    </ul>
                </li>
                <li class="menu-item menu-item-type-custom menu-item-object-custom menu-item-15">
                    <a href="#">EIT Culture & Creativity days</a>
                </li>
            </ul>
        </nav>

        <!-- Secondary Navigation -->
        <nav class="nav-secondary">
            <ul class="nav nav-secondary">
                <li class="menu-item menu-item-type-custom menu-item-object-custom menu-item-has-children menu-item-13">
                    <a href="#" style="display: none;">About Festival</a>
                    <ul class="sub-menu hidden" data-submenu-id="13">
                        <li class="menu-item menu-item-type-post_type menu-item-object-page menu-item-179">
                            <a href="#">Ukážková stránka</a>
                        </li>
                        <li class="menu-item menu-item-type-post_type menu-item-object-page menu-item-180">
                            <a href="#">Ukážková stránka</a>
                        </li>
                        <li class="menu-item menu-item-type-post_type menu-item-object-post menu-item-181">
                            <a href="#">Test Post 2 Recomendation</a>
                        </li>
                    </ul>
                </li>
                <li class="menu-item menu-item-type-custom menu-item-object-custom menu-item-has-children menu-item-14">
                    <a href="#" style="display: none;">Art & Tech conference</a>
                    <ul class="sub-menu hidden" data-submenu-id="14">
                        <li class="menu-item menu-item-type-post_type menu-item-object-post menu-item-182">
                            <a href="#">Test Post 1 Recomendation</a>
                        </li>
                    </ul>
                </li>
            </ul>
        </nav>
    </div>

    <script>
        // Simplified version of the navigation logic to test
        document.addEventListener('DOMContentLoaded', function () {
            const header = document.querySelector('#header');
            const primaryNav = document.querySelector('.nav-primary');
            const secondaryNav = document.querySelector('.nav-secondary');

            if (!header || !primaryNav || !secondaryNav) {
                console.log('Navigation elements not found');
                return;
            }

            const primaryMenuItems = primaryNav.querySelectorAll('.menu-item-has-children');
            const secondaryMenuItems = secondaryNav.querySelectorAll('.sub-menu');

            console.log('Found navigation items:', {
                primaryMenuItems: primaryMenuItems.length,
                secondaryMenuItems: secondaryMenuItems.length
            });

            function getSubmenuId(menuItem) {
                const classes = Array.from(menuItem.classList);
                const menuItemClass = classes.find(cls => cls.startsWith('menu-item-'));
                return menuItemClass ? menuItemClass.replace('menu-item-', '') : null;
            }

            function showSubmenu(submenu) {
                secondaryMenuItems.forEach(item => {
                    item.classList.add('hidden');
                });

                if (submenu) {
                    submenu.classList.remove('hidden');
                    secondaryNav.style.display = 'block';
                }
            }

            function hideAllSubmenus() {
                secondaryMenuItems.forEach(item => {
                    item.classList.add('hidden');
                });
                secondaryNav.style.display = 'none';
            }

            // Add hover event listeners
            primaryMenuItems.forEach((menuItem) => {
                const submenuId = getSubmenuId(menuItem);
                console.log('Menu item ID:', submenuId);
                
                menuItem.addEventListener('mouseenter', function() {
                    const correspondingSubmenu = secondaryNav.querySelector(`[data-submenu-id="${submenuId}"]`);
                    console.log('Looking for submenu with ID:', submenuId, 'Found:', !!correspondingSubmenu);
                    
                    if (correspondingSubmenu) {
                        showSubmenu(correspondingSubmenu);
                    }
                });
            });

            header.addEventListener('mouseleave', function() {
                setTimeout(() => {
                    hideAllSubmenus();
                }, 100);
            });

            // Initialize
            hideAllSubmenus();
        });
    </script>
</body>
</html>
