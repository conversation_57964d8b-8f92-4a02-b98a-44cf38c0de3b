<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Navigation Debug</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .debug-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .debug-section h3 { margin-top: 0; }
        .console-log { background: #f5f5f5; padding: 10px; margin: 10px 0; font-family: monospace; }
        .nav-primary { background: #333; padding: 10px; margin: 10px 0; }
        .nav-secondary { background: #666; padding: 10px; margin: 10px 0; }
        .nav-primary .menu-item { display: inline-block; margin-right: 20px; }
        .nav-primary .menu-item a { color: white; text-decoration: none; padding: 5px 10px; }
        .nav-secondary .sub-menu { display: flex; gap: 10px; }
        .nav-secondary .sub-menu.hidden { display: none; }
        .nav-secondary .sub-menu a { color: white; text-decoration: none; padding: 5px; }
        .nav-secondary > ul > li > a { display: none; }
        .nav-secondary.hidden { display: none; }
    </style>
</head>
<body>
    <h1>Navigation Debug Test</h1>
    
    <div class="debug-section">
        <h3>Test Navigation Structure</h3>
        <div id="header">
            <!-- Primary Navigation -->
            <nav class="nav-primary">
                <ul class="nav">
                    <li class="menu-item menu-item-type-custom menu-item-object-custom menu-item-has-children menu-item-13">
                        <a href="#">About Festival</a>
                        <ul class="sub-menu">
                            <li class="menu-item menu-item-type-post_type menu-item-object-page menu-item-179">
                                <a href="#">Ukážková stránka</a>
                            </li>
                            <li class="menu-item menu-item-type-post_type menu-item-object-page menu-item-180">
                                <a href="#">Ukážková stránka</a>
                            </li>
                            <li class="menu-item menu-item-type-post_type menu-item-object-post menu-item-181">
                                <a href="#">Test Post 2 Recomendation</a>
                            </li>
                        </ul>
                    </li>
                    <li class="menu-item menu-item-type-custom menu-item-object-custom menu-item-has-children menu-item-14">
                        <a href="#">Art & Tech conference</a>
                        <ul class="sub-menu">
                            <li class="menu-item menu-item-type-post_type menu-item-object-post menu-item-182">
                                <a href="#">Test Post 1 Recomendation</a>
                            </li>
                        </ul>
                    </li>
                    <li class="menu-item menu-item-type-custom menu-item-object-custom menu-item-15">
                        <a href="#">EIT Culture & Creativity days</a>
                    </li>
                </ul>
            </nav>

            <!-- Secondary Navigation -->
            <nav class="nav-secondary hidden">
                <ul class="nav nav-secondary">
                    <li class="menu-item menu-item-type-custom menu-item-object-custom menu-item-has-children menu-item-13">
                        <a href="#" style="display: none;">About Festival</a>
                        <ul class="sub-menu hidden" data-submenu-id="13">
                            <li class="menu-item menu-item-type-post_type menu-item-object-page menu-item-179">
                                <a href="#">Ukážková stránka</a>
                            </li>
                            <li class="menu-item menu-item-type-post_type menu-item-object-page menu-item-180">
                                <a href="#">Ukážková stránka</a>
                            </li>
                            <li class="menu-item menu-item-type-post_type menu-item-object-post menu-item-181">
                                <a href="#">Test Post 2 Recomendation</a>
                            </li>
                        </ul>
                    </li>
                    <li class="menu-item menu-item-type-custom menu-item-object-custom menu-item-has-children menu-item-14">
                        <a href="#" style="display: none;">Art & Tech conference</a>
                        <ul class="sub-menu hidden" data-submenu-id="14">
                            <li class="menu-item menu-item-type-post_type menu-item-object-post menu-item-182">
                                <a href="#">Test Post 1 Recomendation</a>
                            </li>
                        </ul>
                    </li>
                </ul>
            </nav>
        </div>
    </div>

    <div class="debug-section">
        <h3>Debug Console</h3>
        <div id="debug-console" class="console-log">Loading...</div>
    </div>

    <script>
        // Debug version of the navigation logic
        document.addEventListener('DOMContentLoaded', function () {
            const debugConsole = document.getElementById('debug-console');
            
            function log(message) {
                debugConsole.innerHTML += message + '<br>';
                console.log(message);
            }
            
            log('=== Navigation Debug Started ===');
            
            const header = document.querySelector('#header');
            const primaryNav = document.querySelector('.nav-primary');
            const secondaryNav = document.querySelector('.nav-secondary');

            log(`Header found: ${!!header}`);
            log(`Primary nav found: ${!!primaryNav}`);
            log(`Secondary nav found: ${!!secondaryNav}`);

            if (!header || !primaryNav || !secondaryNav) {
                log('ERROR: Required navigation elements not found!');
                return;
            }

            // Get all primary menu items that have sub-menus
            const primaryMenuItems = primaryNav.querySelectorAll('.menu-item-has-children');
            const secondaryMenuItems = secondaryNav.querySelectorAll('.sub-menu');

            log(`Primary menu items with children: ${primaryMenuItems.length}`);
            log(`Secondary menu items: ${secondaryMenuItems.length}`);

            if (primaryMenuItems.length === 0 || secondaryMenuItems.length === 0) {
                log('ERROR: No menu items with submenus found!');
                return;
            }

            // Helper function to get submenu ID from menu item
            function getSubmenuId(menuItem) {
                const classes = Array.from(menuItem.classList);
                const menuItemClass = classes.find(cls => cls.startsWith('menu-item-'));
                return menuItemClass ? menuItemClass.replace('menu-item-', '') : null;
            }

            // Helper function to show a submenu
            function showSubmenu(submenu) {
                log(`Showing submenu: ${submenu ? 'found' : 'not found'}`);
                
                // Hide all submenus first
                secondaryMenuItems.forEach(item => {
                    item.classList.add('hidden');
                    item.classList.remove('flex');
                });

                // Show the target submenu
                if (submenu) {
                    submenu.classList.remove('hidden');
                    submenu.classList.add('flex');
                    secondaryNav.classList.remove('hidden');
                    secondaryNav.classList.add('block');
                    log('Submenu should now be visible');
                }
            }

            // Helper function to hide all submenus
            function hideAllSubmenus() {
                log('Hiding all submenus');
                secondaryMenuItems.forEach(item => {
                    item.classList.add('hidden');
                    item.classList.remove('flex');
                });
                secondaryNav.classList.add('hidden');
                secondaryNav.classList.remove('block');
            }

            // Add data attributes to submenus for easier identification
            log('=== Setting up submenu matching ===');
            primaryMenuItems.forEach((parentMenuItem) => {
                const submenuId = getSubmenuId(parentMenuItem);
                log(`Processing primary menu item with ID: ${submenuId}`);
                
                if (submenuId) {
                    // Find the corresponding menu item in secondary navigation with the same ID
                    const correspondingSecondaryMenuItem = secondaryNav.querySelector(`.menu-item-${submenuId}`);
                    log(`Looking for secondary menu item: .menu-item-${submenuId} - Found: ${!!correspondingSecondaryMenuItem}`);
                    
                    if (correspondingSecondaryMenuItem) {
                        const secondarySubmenu = correspondingSecondaryMenuItem.querySelector('.sub-menu');
                        log(`Secondary submenu found: ${!!secondarySubmenu}`);
                        
                        if (secondarySubmenu) {
                            secondarySubmenu.setAttribute('data-submenu-id', submenuId);
                            log(`✓ Matched submenu for menu item ${submenuId}`);
                        }
                    }
                }
            });

            // Add hover event listeners to primary menu items
            log('=== Setting up hover event listeners ===');
            primaryMenuItems.forEach((menuItem) => {
                const submenuId = getSubmenuId(menuItem);
                const correspondingSubmenu = secondaryNav.querySelector(`[data-submenu-id="${submenuId}"]`);
                
                log(`Setting up hover for menu item ${submenuId}, submenu found: ${!!correspondingSubmenu}`);

                if (correspondingSubmenu) {
                    menuItem.addEventListener('mouseenter', function() {
                        log(`HOVER: Mouse entered menu item ${submenuId}`);
                        showSubmenu(correspondingSubmenu);
                    });
                    
                    menuItem.addEventListener('mouseleave', function() {
                        log(`HOVER: Mouse left menu item ${submenuId}`);
                    });
                } else {
                    log(`WARNING: No corresponding submenu found for menu item ${submenuId}`);
                }
            });

            // Track mouse entering/leaving the header
            header.addEventListener('mouseenter', function() {
                log('Mouse entered header');
            });

            header.addEventListener('mouseleave', function() {
                log('Mouse left header');
                setTimeout(() => {
                    hideAllSubmenus();
                }, 100);
            });

            // Initialize: hide all submenus
            log('=== Initializing ===');
            hideAllSubmenus();
            log('Navigation debug setup complete');
        });
    </script>
</body>
</html>
