<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Final Navigation Fix Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .test-section h3 { margin-top: 0; }
        .status { padding: 5px 10px; margin: 5px; border-radius: 3px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .warning { background: #fff3cd; color: #856404; }
        .nav-primary { background: #333; padding: 10px; margin: 10px 0; }
        .nav-secondary { background: #666; padding: 10px; margin: 10px 0; }
        .nav-primary .menu-item { display: inline-block; margin-right: 20px; }
        .nav-primary .menu-item a { color: white; text-decoration: none; padding: 5px 10px; display: block; }
        .nav-primary .menu-item:hover { background: #555; }
        .nav-secondary .sub-menu { display: flex; gap: 10px; }
        .nav-secondary .sub-menu.hidden { display: none; }
        .nav-secondary .sub-menu a { color: white; text-decoration: none; padding: 5px; }
        .nav-secondary > ul > li > a { display: none; }
        .nav-secondary.hidden { display: none; }
        .instructions { background: #e7f3ff; padding: 15px; margin: 10px 0; border-left: 4px solid #2196F3; }
        .expected-behavior { background: #f0f8ff; padding: 10px; margin: 10px 0; border: 1px solid #b3d9ff; }
        .issue-description { background: #fff3cd; padding: 15px; margin: 10px 0; border-left: 4px solid #ffc107; }
    </style>
</head>
<body>
    <h1>Final Navigation Fix Verification Test</h1>

    <div class="issue-description">
        <h3>Issue Description:</h3>
        <p><strong>Problem:</strong> When hovering over or interacting with menu items in the main navigation, the submenu that appears is incorrectly showing the submenu for the first menu item instead of showing the submenu that corresponds to the currently hovered/selected menu item.</p>
        <p><strong>Root Cause:</strong> Missing CSS classes in secondary-navigation.blade.php that hide submenus by default.</p>
        <p><strong>Fix Applied:</strong> Added <code>hidden</code> class to nav element and <code>[&_.sub-menu]:hidden</code> to CSS classes.</p>
    </div>

    <div class="expected-behavior">
        <h3>Expected Behavior After Fix:</h3>
        <ul>
            <li>✅ Hover over "About Festival" → should show ONLY its 3 submenu items</li>
            <li>✅ Hover over "Art & Tech conference" → should show ONLY its 1 submenu item</li>
            <li>✅ Hover over "EIT Culture & Creativity days" → should show no submenu</li>
            <li>✅ Move mouse away from header → submenus should hide</li>
            <li>❌ Should NOT show the first submenu for all menu items</li>
        </ul>
    </div>

    <div class="test-section">
        <h3>Navigation Test (FIXED VERSION)</h3>
        <div id="header">
            <!-- Primary Navigation -->
            <nav class="nav-primary">
                <ul class="nav">
                    <li class="menu-item menu-item-type-custom menu-item-object-custom menu-item-has-children menu-item-13">
                        <a href="#">About Festival</a>
                        <ul class="sub-menu">
                            <li class="menu-item menu-item-type-post_type menu-item-object-page menu-item-179">
                                <a href="#">Ukážková stránka</a>
                            </li>
                            <li class="menu-item menu-item-type-post_type menu-item-object-page menu-item-180">
                                <a href="#">Ukážková stránka</a>
                            </li>
                            <li class="menu-item menu-item-type-post_type menu-item-object-post menu-item-181">
                                <a href="#">Test Post 2 Recomendation</a>
                            </li>
                        </ul>
                    </li>
                    <li class="menu-item menu-item-type-custom menu-item-object-custom menu-item-has-children menu-item-14">
                        <a href="#">Art & Tech conference</a>
                        <ul class="sub-menu">
                            <li class="menu-item menu-item-type-post_type menu-item-object-post menu-item-182">
                                <a href="#">Test Post 1 Recomendation</a>
                            </li>
                        </ul>
                    </li>
                    <li class="menu-item menu-item-type-custom menu-item-object-custom menu-item-15">
                        <a href="#">EIT Culture & Creativity days</a>
                    </li>
                </ul>
            </nav>

            <!-- Secondary Navigation (FIXED VERSION) -->
            <nav class="nav-secondary hidden">
                <ul class="nav nav-secondary">
                    <li class="menu-item menu-item-type-custom menu-item-object-custom menu-item-has-children menu-item-13">
                        <a href="#" style="display: none;">About Festival</a>
                        <ul class="sub-menu hidden">
                            <li class="menu-item menu-item-type-post_type menu-item-object-page menu-item-179">
                                <a href="#">Ukážková stránka</a>
                            </li>
                            <li class="menu-item menu-item-type-post_type menu-item-object-page menu-item-180">
                                <a href="#">Ukážková stránka</a>
                            </li>
                            <li class="menu-item menu-item-type-post_type menu-item-object-post menu-item-181">
                                <a href="#">Test Post 2 Recomendation</a>
                            </li>
                        </ul>
                    </li>
                    <li class="menu-item menu-item-type-custom menu-item-object-custom menu-item-has-children menu-item-14">
                        <a href="#" style="display: none;">Art & Tech conference</a>
                        <ul class="sub-menu hidden">
                            <li class="menu-item menu-item-type-post_type menu-item-object-post menu-item-182">
                                <a href="#">Test Post 1 Recomendation</a>
                            </li>
                        </ul>
                    </li>
                </ul>
            </nav>
        </div>
    </div>

    <div class="test-section">
        <h3>Test Results</h3>
        <div id="test-results">
            <div class="status" id="setup-status">Setting up tests...</div>
        </div>
    </div>

    <div class="test-section">
        <h3>Live Test Log</h3>
        <div id="live-log" style="background: #f5f5f5; padding: 10px; font-family: monospace; white-space: pre-wrap; max-height: 300px; overflow-y: auto;">Waiting for interactions...</div>
    </div>

    <script>
        // FINAL FIXED navigation logic
        document.addEventListener('DOMContentLoaded', function () {
            const resultsDiv = document.getElementById('test-results');
            const setupStatus = document.getElementById('setup-status');
            const liveLog = document.getElementById('live-log');

            function addResult(message, type = 'success') {
                const div = document.createElement('div');
                div.className = `status ${type}`;
                div.textContent = message;
                resultsDiv.appendChild(div);
            }

            function logLive(message) {
                const timestamp = new Date().toLocaleTimeString();
                liveLog.textContent += `[${timestamp}] ${message}\n`;
                liveLog.scrollTop = liveLog.scrollHeight;
            }

            const header = document.querySelector('#header');
            const primaryNav = document.querySelector('.nav-primary');
            const secondaryNav = document.querySelector('.nav-secondary');

            if (!header || !primaryNav || !secondaryNav) {
                addResult('❌ Required navigation elements not found!', 'error');
                return;
            }

            addResult('✅ Navigation elements found');

            const primaryMenuItems = primaryNav.querySelectorAll('.menu-item-has-children');
            const secondaryMenuItems = secondaryNav.querySelectorAll('.sub-menu');

            addResult(`✅ Found ${primaryMenuItems.length} primary menu items with children`);
            addResult(`✅ Found ${secondaryMenuItems.length} secondary menu items`);

            if (primaryMenuItems.length === 0 || secondaryMenuItems.length === 0) {
                addResult('❌ No menu items with submenus found!', 'error');
                return;
            }

            let currentActiveSubmenu = null;
            let hoverTimeout = null;
            let isMouseInHeader = false;

            function getSubmenuId(menuItem) {
                const classes = Array.from(menuItem.classList);
                const menuItemClass = classes.find(cls => cls.startsWith('menu-item-'));
                return menuItemClass ? menuItemClass.replace('menu-item-', '') : null;
            }

            function showSubmenu(submenu) {
                const submenuId = submenu.getAttribute('data-submenu-id');
                logLive(`🎯 SHOWING submenu with ID: ${submenuId}`);

                // Hide all submenus first
                secondaryMenuItems.forEach(item => {
                    item.classList.add('hidden');
                    item.classList.remove('flex');
                });

                // Show the target submenu
                if (submenu) {
                    submenu.classList.remove('hidden');
                    submenu.classList.add('flex');
                    secondaryNav.classList.remove('hidden');
                    secondaryNav.classList.add('block');

                    // Count visible items for verification
                    const visibleItems = submenu.querySelectorAll('li');
                    logLive(`✅ Submenu ${submenuId} now visible with ${visibleItems.length} items`);

                    // Verify this is the correct submenu
                    const expectedCounts = { '13': 3, '14': 1 };
                    const expectedCount = expectedCounts[submenuId];
                    if (expectedCount && visibleItems.length === expectedCount) {
                        logLive(`✅ CORRECT: Submenu ${submenuId} shows ${visibleItems.length} items as expected`);
                    } else if (expectedCount) {
                        logLive(`❌ ERROR: Submenu ${submenuId} shows ${visibleItems.length} items, expected ${expectedCount}`);
                    }
                }
            }

            function hideAllSubmenus() {
                logLive('🔄 Hiding all submenus');
                secondaryMenuItems.forEach(item => {
                    item.classList.add('hidden');
                    item.classList.remove('flex');
                });
                secondaryNav.classList.add('hidden');
                secondaryNav.classList.remove('block');
            }

            // Set up submenu matching (FIXED VERSION)
            logLive('🔧 Setting up submenu matching...');
            primaryMenuItems.forEach((parentMenuItem) => {
                const submenuId = getSubmenuId(parentMenuItem);
                const menuText = parentMenuItem.querySelector('a').textContent.trim();

                if (submenuId) {
                    // Find the corresponding menu item in secondary navigation with the same ID
                    const correspondingSecondaryMenuItem = secondaryNav.querySelector(`.menu-item-${submenuId}`);
                    if (correspondingSecondaryMenuItem) {
                        const secondarySubmenu = correspondingSecondaryMenuItem.querySelector('.sub-menu');
                        if (secondarySubmenu) {
                            secondarySubmenu.setAttribute('data-submenu-id', submenuId);
                            logLive(`✅ Matched "${menuText}" (ID: ${submenuId}) with its submenu`);
                            addResult(`✅ Matched submenu for "${menuText}" (ID: ${submenuId})`);
                        }
                    } else {
                        logLive(`❌ No secondary menu item found for "${menuText}" (ID: ${submenuId})`);
                        addResult(`❌ No secondary menu item found for "${menuText}"`, 'error');
                    }
                }
            });

            // Add hover event listeners (FIXED VERSION)
            primaryMenuItems.forEach((menuItem) => {
                const submenuId = getSubmenuId(menuItem);
                const menuText = menuItem.querySelector('a').textContent.trim();
                const correspondingSubmenu = secondaryNav.querySelector(`[data-submenu-id="${submenuId}"]`);

                if (correspondingSubmenu) {
                    menuItem.addEventListener('mouseenter', function() {
                        clearTimeout(hoverTimeout);
                        isMouseInHeader = true;

                        logLive(`🖱️  HOVER: Mouse entered "${menuText}" (ID: ${submenuId})`);
                        showSubmenu(correspondingSubmenu);
                        currentActiveSubmenu = correspondingSubmenu;
                    });

                    menuItem.addEventListener('mouseleave', function() {
                        logLive(`🖱️  LEAVE: Mouse left "${menuText}" (ID: ${submenuId})`);
                    });

                    addResult(`✅ Event listener added for "${menuText}"`);
                } else {
                    addResult(`⚠️ No submenu found for "${menuText}"`, 'warning');
                }
            });

            // Header mouse events
            header.addEventListener('mouseenter', function() {
                isMouseInHeader = true;
                clearTimeout(hoverTimeout);
                logLive('🖱️  Mouse entered header area');
            });

            header.addEventListener('mouseleave', function() {
                isMouseInHeader = false;
                logLive('🖱️  Mouse left header area - hiding submenus in 100ms');
                hoverTimeout = setTimeout(() => {
                    if (!isMouseInHeader) {
                        hideAllSubmenus();
                    }
                }, 100);
            });

            // Initialize
            hideAllSubmenus();
            setupStatus.textContent = '✅ Navigation setup complete - Test the fix by hovering over menu items!';
            setupStatus.className = 'status success';

            logLive('🚀 Navigation system initialized with FINAL FIX');
            logLive('📝 Test Instructions:');
            logLive('   1. Hover over "About Festival" - should show 3 items');
            logLive('   2. Hover over "Art & Tech conference" - should show 1 item');
            logLive('   3. Hover over "EIT Culture & Creativity days" - should show nothing');
            logLive('   4. Each menu should show ONLY its own submenu, not the first one');
        });
    </script>
</body>
</html>
