<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Submenu Matching</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .debug-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .debug-section h3 { margin-top: 0; }
        .console-log { background: #f5f5f5; padding: 10px; margin: 10px 0; font-family: monospace; white-space: pre-wrap; }
        .nav-primary { background: #333; padding: 10px; margin: 10px 0; }
        .nav-secondary { background: #666; padding: 10px; margin: 10px 0; }
        .nav-primary .menu-item { display: inline-block; margin-right: 20px; }
        .nav-primary .menu-item a { color: white; text-decoration: none; padding: 5px 10px; }
        .nav-secondary .sub-menu { display: flex; gap: 10px; }
        .nav-secondary .sub-menu.hidden { display: none; }
        .nav-secondary .sub-menu a { color: white; text-decoration: none; padding: 5px; }
        .nav-secondary > ul > li > a { display: none; }
        .nav-secondary.hidden { display: none; }
        .highlight { background: yellow; padding: 2px; }
    </style>
</head>
<body>
    <h1>Debug Submenu Matching Issue</h1>
    
    <div class="debug-section">
        <h3>Current Navigation Structure</h3>
        <div id="header">
            <!-- Primary Navigation -->
            <nav class="nav-primary">
                <ul class="nav">
                    <li class="menu-item menu-item-type-custom menu-item-object-custom menu-item-has-children menu-item-13">
                        <a href="#">About Festival</a>
                        <ul class="sub-menu">
                            <li class="menu-item menu-item-type-post_type menu-item-object-page menu-item-179">
                                <a href="#">Ukážková stránka</a>
                            </li>
                            <li class="menu-item menu-item-type-post_type menu-item-object-page menu-item-180">
                                <a href="#">Ukážková stránka</a>
                            </li>
                            <li class="menu-item menu-item-type-post_type menu-item-object-post menu-item-181">
                                <a href="#">Test Post 2 Recomendation</a>
                            </li>
                        </ul>
                    </li>
                    <li class="menu-item menu-item-type-custom menu-item-object-custom menu-item-has-children menu-item-14">
                        <a href="#">Art & Tech conference</a>
                        <ul class="sub-menu">
                            <li class="menu-item menu-item-type-post_type menu-item-object-post menu-item-182">
                                <a href="#">Test Post 1 Recomendation</a>
                            </li>
                        </ul>
                    </li>
                    <li class="menu-item menu-item-type-custom menu-item-object-custom menu-item-15">
                        <a href="#">EIT Culture & Creativity days</a>
                    </li>
                </ul>
            </nav>

            <!-- Secondary Navigation -->
            <nav class="nav-secondary hidden">
                <ul class="nav nav-secondary">
                    <li class="menu-item menu-item-type-custom menu-item-object-custom menu-item-has-children menu-item-13">
                        <a href="#" style="display: none;">About Festival</a>
                        <ul class="sub-menu hidden">
                            <li class="menu-item menu-item-type-post_type menu-item-object-page menu-item-179">
                                <a href="#">Ukážková stránka</a>
                            </li>
                            <li class="menu-item menu-item-type-post_type menu-item-object-page menu-item-180">
                                <a href="#">Ukážková stránka</a>
                            </li>
                            <li class="menu-item menu-item-type-post_type menu-item-object-post menu-item-181">
                                <a href="#">Test Post 2 Recomendation</a>
                            </li>
                        </ul>
                    </li>
                    <li class="menu-item menu-item-type-custom menu-item-object-custom menu-item-has-children menu-item-14">
                        <a href="#" style="display: none;">Art & Tech conference</a>
                        <ul class="sub-menu hidden">
                            <li class="menu-item menu-item-type-post_type menu-item-object-post menu-item-182">
                                <a href="#">Test Post 1 Recomendation</a>
                            </li>
                        </ul>
                    </li>
                </ul>
            </nav>
        </div>
    </div>

    <div class="debug-section">
        <h3>Debug Output</h3>
        <div id="debug-output" class="console-log">Starting debug...</div>
    </div>

    <div class="debug-section">
        <h3>Test Hover Events</h3>
        <p>Hover over the menu items above to see which submenu is actually being shown:</p>
        <div id="hover-output" class="console-log">Waiting for hover events...</div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function () {
            const debugOutput = document.getElementById('debug-output');
            const hoverOutput = document.getElementById('hover-output');
            
            function log(message, isError = false) {
                const timestamp = new Date().toLocaleTimeString();
                const prefix = isError ? '❌ ERROR' : '🔍 DEBUG';
                const logMessage = `[${timestamp}] ${prefix}: ${message}\n`;
                debugOutput.textContent += logMessage;
                console.log(logMessage);
            }
            
            function logHover(message) {
                const timestamp = new Date().toLocaleTimeString();
                const logMessage = `[${timestamp}] 🎯 HOVER: ${message}\n`;
                hoverOutput.textContent += logMessage;
                console.log(logMessage);
            }
            
            log('=== Starting Submenu Matching Debug ===');
            
            const header = document.querySelector('#header');
            const primaryNav = document.querySelector('.nav-primary');
            const secondaryNav = document.querySelector('.nav-secondary');

            log(`Header found: ${!!header}`);
            log(`Primary nav found: ${!!primaryNav}`);
            log(`Secondary nav found: ${!!secondaryNav}`);

            if (!header || !primaryNav || !secondaryNav) {
                log('Required navigation elements not found!', true);
                return;
            }

            const primaryMenuItems = primaryNav.querySelectorAll('.menu-item-has-children');
            const secondaryMenuItems = secondaryNav.querySelectorAll('.sub-menu');

            log(`Primary menu items with children: ${primaryMenuItems.length}`);
            log(`Secondary menu items: ${secondaryMenuItems.length}`);

            // Helper function to get submenu ID from menu item
            function getSubmenuId(menuItem) {
                const classes = Array.from(menuItem.classList);
                const menuItemClass = classes.find(cls => cls.startsWith('menu-item-'));
                return menuItemClass ? menuItemClass.replace('menu-item-', '') : null;
            }

            // Debug the submenu matching process
            log('=== Analyzing Primary Menu Items ===');
            primaryMenuItems.forEach((parentMenuItem, index) => {
                const submenuId = getSubmenuId(parentMenuItem);
                const menuText = parentMenuItem.querySelector('a').textContent.trim();
                log(`Primary Item ${index + 1}: "${menuText}" has ID: ${submenuId}`);
                log(`  Classes: ${Array.from(parentMenuItem.classList).join(', ')}`);
                
                // Check if corresponding secondary menu item exists
                const correspondingSecondaryMenuItem = secondaryNav.querySelector(`.menu-item-${submenuId}`);
                log(`  Looking for secondary item: .menu-item-${submenuId} - Found: ${!!correspondingSecondaryMenuItem}`);
                
                if (correspondingSecondaryMenuItem) {
                    const secondarySubmenu = correspondingSecondaryMenuItem.querySelector('.sub-menu');
                    log(`  Secondary submenu found: ${!!secondarySubmenu}`);
                    
                    if (secondarySubmenu) {
                        // Set the data attribute
                        secondarySubmenu.setAttribute('data-submenu-id', submenuId);
                        log(`  ✅ Set data-submenu-id="${submenuId}" on secondary submenu`);
                        
                        // Count items in submenu
                        const submenuItems = secondarySubmenu.querySelectorAll('li');
                        log(`  Submenu has ${submenuItems.length} items`);
                    }
                } else {
                    log(`  ❌ No corresponding secondary menu item found!`, true);
                }
            });

            log('=== Analyzing Secondary Menu Items ===');
            secondaryMenuItems.forEach((submenu, index) => {
                const dataId = submenu.getAttribute('data-submenu-id');
                const parentLi = submenu.closest('li');
                const parentClasses = parentLi ? Array.from(parentLi.classList).join(', ') : 'none';
                log(`Secondary Submenu ${index + 1}: data-submenu-id="${dataId}"`);
                log(`  Parent LI classes: ${parentClasses}`);
                
                const submenuItems = submenu.querySelectorAll('li');
                log(`  Contains ${submenuItems.length} items`);
            });

            // Helper functions for showing/hiding submenus
            function showSubmenu(submenu) {
                const submenuId = submenu.getAttribute('data-submenu-id');
                logHover(`Attempting to show submenu with ID: ${submenuId}`);
                
                // Hide all submenus first
                secondaryMenuItems.forEach(item => {
                    item.classList.add('hidden');
                    item.classList.remove('flex');
                });

                // Show the target submenu
                if (submenu) {
                    submenu.classList.remove('hidden');
                    submenu.classList.add('flex');
                    secondaryNav.classList.remove('hidden');
                    secondaryNav.classList.add('block');
                    logHover(`✅ Submenu ${submenuId} should now be visible`);
                }
            }

            function hideAllSubmenus() {
                logHover('Hiding all submenus');
                secondaryMenuItems.forEach(item => {
                    item.classList.add('hidden');
                    item.classList.remove('flex');
                });
                secondaryNav.classList.add('hidden');
                secondaryNav.classList.remove('block');
            }

            // Add hover event listeners with detailed logging
            log('=== Setting Up Hover Event Listeners ===');
            primaryMenuItems.forEach((menuItem, index) => {
                const submenuId = getSubmenuId(menuItem);
                const menuText = menuItem.querySelector('a').textContent.trim();
                
                log(`Setting up hover for "${menuText}" (ID: ${submenuId})`);
                
                // Look for corresponding submenu
                const correspondingSubmenu = secondaryNav.querySelector(`[data-submenu-id="${submenuId}"]`);
                log(`  Corresponding submenu found: ${!!correspondingSubmenu}`);
                
                if (correspondingSubmenu) {
                    menuItem.addEventListener('mouseenter', function() {
                        logHover(`Mouse entered "${menuText}" (ID: ${submenuId})`);
                        showSubmenu(correspondingSubmenu);
                    });
                    
                    menuItem.addEventListener('mouseleave', function() {
                        logHover(`Mouse left "${menuText}" (ID: ${submenuId})`);
                    });
                    
                    log(`  ✅ Event listeners added for "${menuText}"`);
                } else {
                    log(`  ❌ No corresponding submenu found for "${menuText}"`, true);
                }
            });

            // Header leave event
            header.addEventListener('mouseleave', function() {
                logHover('Mouse left header - hiding submenus in 100ms');
                setTimeout(() => {
                    hideAllSubmenus();
                }, 100);
            });

            // Initialize
            hideAllSubmenus();
            log('=== Debug Setup Complete ===');
            log('Now hover over the menu items to test the functionality');
        });
    </script>
</body>
</html>
