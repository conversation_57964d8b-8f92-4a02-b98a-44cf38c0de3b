<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Navigation Fix Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .test-section h3 { margin-top: 0; }
        .status { padding: 5px 10px; margin: 5px; border-radius: 3px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .nav-primary { background: #333; padding: 10px; margin: 10px 0; }
        .nav-secondary { background: #666; padding: 10px; margin: 10px 0; }
        .nav-primary .menu-item { display: inline-block; margin-right: 20px; }
        .nav-primary .menu-item a { color: white; text-decoration: none; padding: 5px 10px; display: block; }
        .nav-primary .menu-item:hover { background: #555; }
        .nav-secondary .sub-menu { display: flex; gap: 10px; }
        .nav-secondary .sub-menu.hidden { display: none; }
        .nav-secondary .sub-menu a { color: white; text-decoration: none; padding: 5px; }
        .nav-secondary > ul > li > a { display: none; }
        .nav-secondary.hidden { display: none; }
        .instructions { background: #e7f3ff; padding: 15px; margin: 10px 0; border-left: 4px solid #2196F3; }
    </style>
</head>
<body>
    <h1>Navigation Fix Verification Test</h1>
    
    <div class="instructions">
        <h3>Test Instructions:</h3>
        <ol>
            <li>Hover over "About Festival" - should show 3 submenu items</li>
            <li>Hover over "Art & Tech conference" - should show 1 submenu item</li>
            <li>Hover over "EIT Culture & Creativity days" - should show no submenu (no secondary nav)</li>
            <li>Move mouse away from header - submenus should hide</li>
        </ol>
    </div>
    
    <div class="test-section">
        <h3>Navigation Test</h3>
        <div id="header">
            <!-- Primary Navigation -->
            <nav class="nav-primary">
                <ul class="nav">
                    <li class="menu-item menu-item-type-custom menu-item-object-custom menu-item-has-children menu-item-13">
                        <a href="#">About Festival</a>
                        <ul class="sub-menu">
                            <li class="menu-item menu-item-type-post_type menu-item-object-page menu-item-179">
                                <a href="#">Ukážková stránka</a>
                            </li>
                            <li class="menu-item menu-item-type-post_type menu-item-object-page menu-item-180">
                                <a href="#">Ukážková stránka</a>
                            </li>
                            <li class="menu-item menu-item-type-post_type menu-item-object-post menu-item-181">
                                <a href="#">Test Post 2 Recomendation</a>
                            </li>
                        </ul>
                    </li>
                    <li class="menu-item menu-item-type-custom menu-item-object-custom menu-item-has-children menu-item-14">
                        <a href="#">Art & Tech conference</a>
                        <ul class="sub-menu">
                            <li class="menu-item menu-item-type-post_type menu-item-object-post menu-item-182">
                                <a href="#">Test Post 1 Recomendation</a>
                            </li>
                        </ul>
                    </li>
                    <li class="menu-item menu-item-type-custom menu-item-object-custom menu-item-15">
                        <a href="#">EIT Culture & Creativity days</a>
                    </li>
                </ul>
            </nav>

            <!-- Secondary Navigation -->
            <nav class="nav-secondary hidden">
                <ul class="nav nav-secondary">
                    <li class="menu-item menu-item-type-custom menu-item-object-custom menu-item-has-children menu-item-13">
                        <a href="#" style="display: none;">About Festival</a>
                        <ul class="sub-menu hidden">
                            <li class="menu-item menu-item-type-post_type menu-item-object-page menu-item-179">
                                <a href="#">Ukážková stránka</a>
                            </li>
                            <li class="menu-item menu-item-type-post_type menu-item-object-page menu-item-180">
                                <a href="#">Ukážková stránka</a>
                            </li>
                            <li class="menu-item menu-item-type-post_type menu-item-object-post menu-item-181">
                                <a href="#">Test Post 2 Recomendation</a>
                            </li>
                        </ul>
                    </li>
                    <li class="menu-item menu-item-type-custom menu-item-object-custom menu-item-has-children menu-item-14">
                        <a href="#" style="display: none;">Art & Tech conference</a>
                        <ul class="sub-menu hidden">
                            <li class="menu-item menu-item-type-post_type menu-item-object-post menu-item-182">
                                <a href="#">Test Post 1 Recomendation</a>
                            </li>
                        </ul>
                    </li>
                </ul>
            </nav>
        </div>
    </div>

    <div class="test-section">
        <h3>Test Results</h3>
        <div id="test-results">
            <div class="status" id="setup-status">Setting up tests...</div>
        </div>
    </div>

    <script>
        // Fixed navigation logic (simplified version)
        document.addEventListener('DOMContentLoaded', function () {
            const resultsDiv = document.getElementById('test-results');
            const setupStatus = document.getElementById('setup-status');
            
            function addResult(message, isSuccess = true) {
                const div = document.createElement('div');
                div.className = `status ${isSuccess ? 'success' : 'error'}`;
                div.textContent = message;
                resultsDiv.appendChild(div);
            }
            
            const header = document.querySelector('#header');
            const primaryNav = document.querySelector('.nav-primary');
            const secondaryNav = document.querySelector('.nav-secondary');

            if (!header || !primaryNav || !secondaryNav) {
                addResult('❌ Required navigation elements not found!', false);
                return;
            }

            addResult('✅ Navigation elements found');

            const primaryMenuItems = primaryNav.querySelectorAll('.menu-item-has-children');
            const secondaryMenuItems = secondaryNav.querySelectorAll('.sub-menu');

            addResult(`✅ Found ${primaryMenuItems.length} primary menu items with children`);
            addResult(`✅ Found ${secondaryMenuItems.length} secondary menu items`);

            if (primaryMenuItems.length === 0 || secondaryMenuItems.length === 0) {
                addResult('❌ No menu items with submenus found!', false);
                return;
            }

            let currentActiveSubmenu = null;
            let hoverTimeout = null;
            let isMouseInHeader = false;

            function getSubmenuId(menuItem) {
                const classes = Array.from(menuItem.classList);
                const menuItemClass = classes.find(cls => cls.startsWith('menu-item-'));
                return menuItemClass ? menuItemClass.replace('menu-item-', '') : null;
            }

            function showSubmenu(submenu) {
                secondaryMenuItems.forEach(item => {
                    item.classList.add('hidden');
                    item.classList.remove('flex');
                });

                if (submenu) {
                    submenu.classList.remove('hidden');
                    submenu.classList.add('flex');
                    secondaryNav.classList.remove('hidden');
                    secondaryNav.classList.add('block');
                }
            }

            function hideAllSubmenus() {
                secondaryMenuItems.forEach(item => {
                    item.classList.add('hidden');
                    item.classList.remove('flex');
                });
                secondaryNav.classList.add('hidden');
                secondaryNav.classList.remove('block');
            }

            // Set up submenu matching
            primaryMenuItems.forEach((parentMenuItem) => {
                const submenuId = getSubmenuId(parentMenuItem);
                if (submenuId) {
                    const correspondingSecondaryMenuItem = secondaryNav.querySelector(`.menu-item-${submenuId}`);
                    if (correspondingSecondaryMenuItem) {
                        const secondarySubmenu = correspondingSecondaryMenuItem.querySelector('.sub-menu');
                        if (secondarySubmenu) {
                            secondarySubmenu.setAttribute('data-submenu-id', submenuId);
                            addResult(`✅ Matched submenu for menu item ${submenuId}`);
                        }
                    }
                }
            });

            // Add hover event listeners (FIXED VERSION)
            primaryMenuItems.forEach((menuItem) => {
                const submenuId = getSubmenuId(menuItem);
                const correspondingSubmenu = secondaryNav.querySelector(`[data-submenu-id="${submenuId}"]`);

                if (correspondingSubmenu) {
                    menuItem.addEventListener('mouseenter', function() {
                        clearTimeout(hoverTimeout);
                        isMouseInHeader = true; // FIXED: Set this when hovering over menu items

                        showSubmenu(correspondingSubmenu);
                        currentActiveSubmenu = correspondingSubmenu;
                        addResult(`🎯 Showing submenu for: ${menuItem.textContent.trim()}`);
                    });
                    
                    addResult(`✅ Event listener added for: ${menuItem.textContent.trim()}`);
                } else {
                    addResult(`⚠️ No submenu found for: ${menuItem.textContent.trim()}`);
                }
            });

            // Header mouse events
            header.addEventListener('mouseenter', function() {
                isMouseInHeader = true;
                clearTimeout(hoverTimeout);
            });

            header.addEventListener('mouseleave', function() {
                isMouseInHeader = false;
                hoverTimeout = setTimeout(() => {
                    if (!isMouseInHeader) {
                        hideAllSubmenus();
                        addResult('🔄 Hiding submenus (mouse left header)');
                    }
                }, 100);
            });

            // Initialize
            hideAllSubmenus();
            setupStatus.textContent = '✅ Navigation setup complete - Test by hovering over menu items!';
            setupStatus.className = 'status success';
        });
    </script>
</body>
</html>
