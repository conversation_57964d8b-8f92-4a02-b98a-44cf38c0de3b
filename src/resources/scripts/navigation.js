// Secondary sub-menu functionality for primary navigation
document.addEventListener('DOMContentLoaded', function () {
  const header = document.querySelector('#header');
  const primaryNav = document.querySelector('.nav-primary');
  const secondaryNav = document.querySelector('.nav-secondary');

  if (!header || !primaryNav || !secondaryNav) {
    console.log('Navigation elements not found:', { header: !!header, primaryNav: !!primaryNav, secondaryNav: !!secondaryNav });
    return; // Exit if required elements are not found
  }

  // Get all primary menu items that have sub-menus
  const primaryMenuItems = primaryNav.querySelectorAll('.menu-item-has-children');
  const secondaryMenuItems = secondaryNav.querySelectorAll('.sub-menu');

  console.log('Found navigation items:', {
    primaryMenuItems: primaryMenuItems.length,
    secondaryMenuItems: secondaryMenuItems.length
  });

  // If no menu items with children found, exit early
  if (primaryMenuItems.length === 0 || secondaryMenuItems.length === 0) {
    console.log('No menu items with submenus found');
    return;
  }

  let currentActiveSubmenu = null;
  let hoverTimeout = null;
  let isMouseInHeader = false;

  // Find the currently active page's sub-menu (if any)
  const activeMenuItem = primaryNav.querySelector('.current-menu-item, .current-menu-ancestor, .current-page-ancestor');
  let defaultActiveSubmenu = null;

  if (activeMenuItem && activeMenuItem.classList.contains('menu-item-has-children')) {
    const activeSubmenuId = getSubmenuId(activeMenuItem);
    defaultActiveSubmenu = secondaryNav.querySelector(`[data-submenu-id="${activeSubmenuId}"]`);
    if (defaultActiveSubmenu) {
      showSubmenu(defaultActiveSubmenu);
      highlightActiveSubmenuItem(defaultActiveSubmenu);
      currentActiveSubmenu = defaultActiveSubmenu;
    }
  }

  // Helper function to get submenu ID from menu item
  function getSubmenuId(menuItem) {
    // WordPress typically adds classes like 'menu-item-123' to menu items
    const classes = Array.from(menuItem.classList);
    const menuItemClass = classes.find(cls => cls.startsWith('menu-item-'));
    return menuItemClass ? menuItemClass.replace('menu-item-', '') : null;
  }

  // Helper function to show a submenu
  function showSubmenu(submenu) {
    // Hide all submenus first
    secondaryMenuItems.forEach(item => {
      item.classList.add('hidden');
      item.classList.remove('flex');
    });

    // Show the target submenu
    if (submenu) {
      const submenuId = submenu.getAttribute('data-submenu-id');
      console.log(`📋 Showing submenu with ID: ${submenuId}`);

      submenu.classList.remove('hidden');
      submenu.classList.add('flex');
      secondaryNav.classList.remove('hidden');
      secondaryNav.classList.add('block');
    }
  }

  // Helper function to hide all submenus
  function hideAllSubmenus() {
    secondaryMenuItems.forEach(item => {
      item.classList.add('hidden');
      item.classList.remove('flex');
    });
    secondaryNav.classList.add('hidden');
    secondaryNav.classList.remove('block');
  }

  // Helper function to highlight active submenu item
  function highlightActiveSubmenuItem(submenu) {
    if (!submenu) return;

    const activeItem = submenu.querySelector('.current-menu-item a, .current-page-ancestor a');
    if (activeItem) {
      activeItem.style.textDecoration = 'underline';
    }
  }

  // Add data attributes to submenus for easier identification
  // Match submenus with their parent menu items using WordPress menu item IDs
  primaryMenuItems.forEach((parentMenuItem) => {
    const submenuId = getSubmenuId(parentMenuItem);
    const menuText = parentMenuItem.querySelector('a')?.textContent?.trim() || 'Unknown';

    console.log(`Processing primary menu item: "${menuText}" with ID: ${submenuId}`);

    if (submenuId) {
      // Find the corresponding menu item in secondary navigation with the same ID
      const correspondingSecondaryMenuItem = secondaryNav.querySelector(`.menu-item-${submenuId}`);
      if (correspondingSecondaryMenuItem) {
        const secondarySubmenu = correspondingSecondaryMenuItem.querySelector('.sub-menu');
        if (secondarySubmenu) {
          secondarySubmenu.setAttribute('data-submenu-id', submenuId);
          console.log(`✅ Successfully matched submenu for "${menuText}" (ID: ${submenuId})`);
        } else {
          console.log(`❌ No submenu found in secondary nav for "${menuText}" (ID: ${submenuId})`);
        }
      } else {
        console.log(`❌ No corresponding secondary menu item found for "${menuText}" (ID: ${submenuId})`);
      }
    } else {
      console.log(`❌ Could not extract submenu ID for "${menuText}"`);
    }
  });

  // Add hover event listeners to primary menu items
  primaryMenuItems.forEach((menuItem) => {
    const submenuId = getSubmenuId(menuItem);
    const menuText = menuItem.querySelector('a')?.textContent?.trim() || 'Unknown';
    const correspondingSubmenu = secondaryNav.querySelector(`[data-submenu-id="${submenuId}"]`);

    console.log(`Setting up hover for "${menuText}" (ID: ${submenuId}), submenu found: ${!!correspondingSubmenu}`);

    if (correspondingSubmenu) {
      menuItem.addEventListener('mouseenter', function() {
        clearTimeout(hoverTimeout);
        isMouseInHeader = true; // Set this when hovering over menu items

        console.log(`🖱️ Hovering over "${menuText}" - showing submenu with ID: ${submenuId}`);

        // Show the corresponding submenu
        showSubmenu(correspondingSubmenu);
        currentActiveSubmenu = correspondingSubmenu;
      });
    } else {
      console.log(`⚠️ No submenu found for "${menuText}" (ID: ${submenuId})`);
    }
  });

  // Track mouse entering/leaving the header
  header.addEventListener('mouseenter', function() {
    isMouseInHeader = true;
    clearTimeout(hoverTimeout);
  });

  header.addEventListener('mouseleave', function() {
    isMouseInHeader = false;

    // Add a small delay to prevent flickering
    hoverTimeout = setTimeout(() => {
      if (!isMouseInHeader) {
        if (defaultActiveSubmenu) {
          // Revert to showing the active page's submenu
          showSubmenu(defaultActiveSubmenu);
          currentActiveSubmenu = defaultActiveSubmenu;
        } else {
          // Hide all submenus if no active page submenu
          hideAllSubmenus();
          currentActiveSubmenu = null;
        }
      }
    }, 100);
  });

  // Handle edge case: if mouse moves to secondary nav area, keep it visible
  secondaryNav.addEventListener('mouseenter', function() {
    clearTimeout(hoverTimeout);
    isMouseInHeader = true;
  });

  secondaryNav.addEventListener('mouseleave', function() {
    // Check if mouse is still in header area
    setTimeout(() => {
      const headerRect = header.getBoundingClientRect();
      const mouseEvent = new MouseEvent('mousemove');

      // If mouse is not in header, trigger the leave behavior
      if (!header.matches(':hover')) {
        isMouseInHeader = false;
        if (defaultActiveSubmenu) {
          showSubmenu(defaultActiveSubmenu);
          currentActiveSubmenu = defaultActiveSubmenu;
        } else {
          hideAllSubmenus();
          currentActiveSubmenu = null;
        }
      }
    }, 10);
  });

  // Initialize: hide all submenus except the default active one
  if (!defaultActiveSubmenu) {
    hideAllSubmenus();
  }
});
