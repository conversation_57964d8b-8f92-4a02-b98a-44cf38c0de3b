<header
  id="header"
  class="relative flex bg-dark max-h-[60px] justify-between lg:max-h-[186px]">
  @if( !empty($logoBackground) && wp_is_mobile())
    <img src="{{ $logoBackground['url'] }}" alt="{{ $logoBackground['alt'] }}"
         class="h-full  lg:object-cover lg:object-right absolute left-0 top-0 max-h-[60px] lg:max-h-[186px]
         lg:w-[37.1527777778%]">
  @endif

  <div class="container relative flex items-center lg:items-stretch justify-between px-0">
    <div class="flex items-center relative lg:flex-auto">
      @if( !empty($logoBackground) && !wp_is_mobile())
        <div class="absolute top-0 right-0 bottom-0 lg:w-[42vw] header-bg">
          <img src="{{ $logoBackground['url'] }}" alt="{{ $logoBackground['alt'] }}"
               class="h-full w-full  lg:object-cover lg:object-right ">
        </div>
      @endif
      <div
        class="px-1.5 py-1.5 h-full max-h-[88px] lg:pl-16 lg:flex lg:items-center lg:gap-x-2 relative z-1">
        @include('sections.components.logo')
      </div>
    </div>

    <div class="lg:flex lg:flex-col lg:items-end lg:max-w-[62.8472222222%] lg:w-full flex items-center">
      <div class="px-4 lg:px-16 py-4 lg:py-5 flex gap-x-6">
        <div class="flex items-center gap-x-6 lg:gap-x-8">
          @include('sections.components.top-bar')
        </div>
      </div>

      <div class="w-full border-b-2 border-b-light hidden lg:block">
        @include('sections.components.primary-navigation')
      </div>

      <div class="flex">
        <div class="w-full hidden lg:block">
          @include('sections.components.secondary-navigation')
        </div>

        @if( !empty($header_cta))
          <x-button :button="$header_cta" class="!hidden lg:!flex" />
        @else
          <div class="h-14"></div>
        @endif
      </div>
    </div>
  </div>
</header>

<div class="mobile-menu-hamburger lg:hidden bg-primary border-grey border-2 border-solid fixed bottom-4 right-4
z-40 w-14 h-14 justify-center items-center flex cursor-pointer">
  <x-icons.icon-hamburger-menu class="closed" />

  <x-icons.icon-close class="opened hidden" />
</div>

<div class="mobile-menu fixed w-[calc(100%-32px)] bg-primary h-auto bottom-[84px] left-0 right-0 mx-auto z-50 py-6
px-4 flex flex-col gap-10">
  <nav class="nav-primary mobile">
    {!! wp_nav_menu(['theme_location' => 'primary_navigation', 'menu_class' => 'nav flex flex-col items-start
    text-dark text-xl uppercase gap-y-4 justify-start text-left font-medium', 'echo'
     => false]) !!}
  </nav>

  <hr class="text-dark">

  <div class="[&_img]:invert [&_img]:h-6 [&_img]:w-auto">
    <x-social-icons />
  </div>

  @if( !empty($header_cta))
    <x-button :button="$header_cta" type="secondary-dark" />
  @endif
</div>
